import type { Statistics, StatisticsService } from '../../../domain/estate/estate-statistics/statistics.service';
import type { EstateMongooseRepository } from '../../../domain/estate/mongo/estate.mogoose-repository';
import type { Identity } from '../../../domain/identity/identity';
import { UserRole } from '../../../domain/identity/identity';
import { estateMongooseRepositoryFixtureFactory } from '../../../framework/test/fixtures/repositories/estateMongooseRepositoryFixtureFactory';
import { statisticsServiceFixtureFactory } from '../../../framework/test/fixtures/services/statisticsServiceFixtureFactory';
import { estateMongooseFactory } from '../../../framework/test/helpers/estate-mongoose-factory';
import type { GetEstateStatisticsUseCase, Input } from './get-estate-statistics.use-case';
import { getEstateStatisticsUseCaseFactory } from './get-estate-statistics.use-case';

describe('get estate statistics use case', () => {
  describe('#getEstateStatisticsUseCaseFactory', () => {
    let mongoEstateRepository: EstateMongooseRepository;
    let statisticsService: StatisticsService;
    let getEstateStatisticsUseCase: GetEstateStatisticsUseCase;

    beforeEach(() => {
      mongoEstateRepository = estateMongooseRepositoryFixtureFactory();
      statisticsService = statisticsServiceFixtureFactory();
      getEstateStatisticsUseCase = getEstateStatisticsUseCaseFactory({
        mongoEstateRepository,
        statisticsService,
      });
    });

    describe('given an identity', () => {
      const identity: Identity = {
        userID: '[fake user id]',
        role: UserRole.USER,
        phoneNumber: '[fake phoneNumber]',
      };

      const input: Input = {
        estateId: '[fake vitec id]',
        identity,
      };

      describe('given an existing vitec id', () => {
        beforeEach(() => {
          mongoEstateRepository.findEstateByVitecEstateId = jest.fn().mockResolvedValueOnce(estateMongooseFactory());
        });

        describe('statistics section', () => {
          const expectedStatistics: Statistics = {
            finnNo: {
              adViews: 1,
              receivedAdByEmail: 2,
              savedAd: 3,
              adUrl: '4',
            },
            nordvikboligNo: {
              totalNumberOfPageViews: 1,
              dailyPageViews: [2],
              numberOfSalesProspectDownloads: 3,
              numberOfDirectPageViews: 4,
              numberOfPaidFinnNoPageViews: 5,
              numberOfFinnNoPageViews: 6,
              numberOfFacebookPageViews: 7,
              numberOfGooglePageViews: 8,
            },
            hjemNo: null,
          };

          beforeEach(() => {
            statisticsService.getStatistics = jest.fn().mockResolvedValueOnce(expectedStatistics);
          });

          it('should return the statistics belonging to the estate', async () => {
            const output = await getEstateStatisticsUseCase(input);
            expect(output).toMatchObject(expectedStatistics);
          });
        });
      });
    });
  });
});
