import type { HjemNoConfig } from '../../../config';
import type { Statistics, StatisticsService } from '../../../domain/estate/estate-statistics/statistics.service';
import type { EstateMongooseRepository } from '../../../domain/estate/mongo/estate.mogoose-repository';
import type { WithIdentity } from '../../../domain/identity/identity';
import { BadRequest } from '../../../framework/errors/bad-request.error';
import { ResourceNotFound } from '../../../framework/errors/resource.errors';
import type { AsyncUseCase } from '../../../framework/use-case/async.use-case';

export type Input = WithIdentity<{ estateId: string }>;
type Output = Statistics;

export type GetEstateStatisticsUseCase = AsyncUseCase<Input, Output>;

export const getEstateStatisticsUseCaseFactory = ({
  mongoEstateRepository,
  statisticsService,
  hjemNoConfig,
}: {
  mongoEstateRepository: EstateMongooseRepository;
  statisticsService: StatisticsService;
  hjemNoConfig?: HjemNoConfig;
}): GetEstateStatisticsUseCase => async (input: Input): Promise<Output> => {
  const vitecEstate = await mongoEstateRepository.findEstateByVitecEstateId(input.estateId);

  if (!vitecEstate) {
    throw new ResourceNotFound('No such estate');
  }

  const isBuyingTheEstate = mongoEstateRepository.checkIfBuyingProperty(input.identity.phoneNumber, vitecEstate);

  if (isBuyingTheEstate) {
    throw new BadRequest('errors.buyerIsNotPermittedToSee');
  }

  return statisticsService.getStatistics(
    vitecEstate.finnCode,
    vitecEstate.estateId,
    hjemNoConfig?.systemName,
    hjemNoConfig?.installationId,
  );
};
