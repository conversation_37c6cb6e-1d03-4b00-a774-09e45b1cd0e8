import React, { useState } from 'react';
import { styled } from '@mui/material';
import { ProfileButton } from './ProfileButton';
import RegularModal from '../modal/RegularModal';
import TextField from '../input/TextField';
import { GreenButton } from '../button/Button';
import ToastNotification from '../toast-notification/ToastNotification';
import getConfig from 'next/config';

interface User {
  id: string;
  email: string;
  name: string;
  phoneNumber: string;
  passwordCode: string;
}

interface ImpersonationComponentProps {
  isEmployee: boolean;
}

const ImpersonationComponent: React.FC<ImpersonationComponentProps> = ({ isEmployee }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [apiKey, setApiKey] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [notification, setNotification] = useState<{ visible: boolean; message: string }>({
    visible: false,
    message: '',
  });

  const apiUrl = getConfig().publicRuntimeConfig.api.url;

  const showNotification = (message: string) => {
    setNotification({ visible: true, message });
  };

  const hideNotification = () => {
    setNotification({ visible: false, message: '' });
  };

  const searchUsers = async () => {
    if (!apiKey.trim() || !searchQuery.trim()) {
      showNotification('Vennligst skriv inn både API-nøkkel og søkeord');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${apiUrl}/admin/users/search?query=${encodeURIComponent(searchQuery)}&limit=10`, {
        method: 'GET',
        headers: {
          'x-api-key': apiKey,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Kunne ikke søke etter brukere');
      }

      const data = await response.json();
      setUsers(data.users || []);
    } catch (error) {
      console.error('Error searching users:', error);
      showNotification(error instanceof Error ? error.message : 'Kunne ikke søke etter brukere');
      setUsers([]);
    } finally {
      setIsLoading(false);
    }
  };

  const impersonateUser = async (userId: string) => {
    if (!apiKey.trim()) {
      showNotification('API-nøkkel er påkrevd');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${apiUrl}/admin/users/impersonate`, {
        method: 'POST',
        headers: {
          'x-api-key': apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Kunne ikke imitere bruker');
      }

      const data = await response.json();
      const token = data.token;

      // Store the token and reload the page to impersonate the user
      localStorage.setItem('accessToken', token);
      showNotification('Imitering vellykket! Laster siden på nytt...');

      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (error) {
      console.error('Error impersonating user:', error);
      showNotification(error instanceof Error ? error.message : 'Kunne ikke imitere bruker');
    } finally {
      setIsLoading(false);
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setApiKey('');
    setSearchQuery('');
    setUsers([]);
  };

  if (!isEmployee) {
    return null;
  }

  return (
    <>
      <ToastNotification
        message={notification.message}
        isOpen={notification.visible}
        onClose={hideNotification}
        vertical="top"
        horizontal="center"
      />

      <ProfileButton color="#d7b180" text="Imiter bruker" onClick={() => setIsModalOpen(true)} />

      <RegularModal isOpen={isModalOpen} onClose={handleModalClose}>
        <h2>Brukerimitering</h2>
        <ModalContent>
          <p>Skriv inn din API-nøkkel og søk etter en bruker å imitere.</p>

          <TextField
            label="API-nøkkel"
            type="password"
            value={apiKey}
            onChange={e => setApiKey(e.target.value)}
            placeholder="Skriv inn din admin API-nøkkel"
          />

          <SearchSection>
            <TextField
              label="Søk brukere"
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              placeholder="Søk etter navn, e-post eller telefon"
            />
            <GreenButton size="small" onClick={searchUsers} disabled={isLoading}>
              {isLoading ? 'Søker...' : 'Søk'}
            </GreenButton>
          </SearchSection>

          {users.length > 0 && (
            <UserList>
              <h3>Søkeresultater:</h3>
              {users.map(user => (
                <UserItem key={user.id} onClick={() => impersonateUser(user.id)}>
                  <UserInfo>
                    <UserName>{user.name}</UserName>
                    <UserEmail>{user.email}</UserEmail>
                    <UserPhone>{user.phoneNumber}</UserPhone>
                  </UserInfo>
                  <ImpersonateButton disabled={isLoading}>{isLoading ? 'Laster...' : 'Imiter'}</ImpersonateButton>
                </UserItem>
              ))}
            </UserList>
          )}
        </ModalContent>
      </RegularModal>
    </>
  );
};

const ModalContent = styled('div')({
  padding: '20px 0',
  maxWidth: '500px',
});

const SearchSection = styled('div')({
  display: 'flex',
  gap: '10px',
  alignItems: 'flex-end',
  marginBottom: '20px',

  '& > div:first-of-type': {
    flex: 1,
  },
});

const UserList = styled('div')({
  marginTop: '20px',
});

const UserItem = styled('div')({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '12px',
  border: '1px solid #e0e0e0',
  borderRadius: '8px',
  marginBottom: '8px',
  cursor: 'pointer',
  transition: 'background-color 0.2s',

  '&:hover': {
    backgroundColor: '#f5f5f5',
  },
});

const UserInfo = styled('div')({
  flex: 1,
});

const UserName = styled('div')({
  fontWeight: 'bold',
  marginBottom: '4px',
});

const UserEmail = styled('div')({
  color: '#666',
  fontSize: '14px',
  marginBottom: '2px',
});

const UserPhone = styled('div')({
  color: '#666',
  fontSize: '14px',
});

const ImpersonateButton = styled('button')({
  padding: '8px 16px',
  backgroundColor: '#174a5b',
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  cursor: 'pointer',
  fontSize: '14px',

  '&:hover:not(:disabled)': {
    backgroundColor: '#0f3a47',
  },

  '&:disabled': {
    backgroundColor: '#ccc',
    cursor: 'not-allowed',
  },
});

export default ImpersonationComponent;
