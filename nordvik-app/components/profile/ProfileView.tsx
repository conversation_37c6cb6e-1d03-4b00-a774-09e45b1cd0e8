import { styled } from '@mui/material';
import { useRouter } from 'next/router';
import React, { useContext } from 'react';
import { t } from '../../i18n';
import AnalyticsContext, {
  AnalyticsElement,
  AnalyticsEventName,
  AnalyticsLocation,
} from '../../lib/analytics/AnalyticsContext';
import { ReferralJourneyAnalyticsEvent } from '../../lib/analytics/AnalyticsEvents';
import GlobalCustomerContext from '../../lib/context/GlobalCustomerContext';
import { CustomerProfile, RegisteredWith } from '../../lib/customer/Customer';
import CustomerRepository from '../../lib/customer/CustomerRepository';
import { FeatureFlagName } from '../../lib/feature-flag/FeatureFlag';
import styles from '../../pages/customer/Profile.module.scss';
import PrivacyPolicyModal from '../../pages/privacy-policy';
import { GreenBorderButton, GreenButton } from '../button/Button';
import TermsAndConditionsModal from '../customer/terms/TermsAndConditionsModal';
import FeatureFlag from '../feature-flag/FeatureFlag';
import TextField from '../input/TextField';
import RegularModal from '../modal/RegularModal';
import ToastNotification from '../toast-notification/ToastNotification';
import ChangeEmailAddressForm from './forms/ChangeEmailAddressForm';
import ChangePasswordForm from './forms/ChangePasswordForm';
import ConsentSettingsForm from './forms/ConsentSettingsForm';
import DeleteUserForm from './forms/deleteUserForm';
import NotificationSettingsForm from './forms/NotificationSettingsForm';
import { ProfileButton } from './ProfileButton';
import { ProfileCookieMenu } from './ProfileCookieMenu';
import ImpersonationComponent from './ImpersonationComponent';

export default function ProfileView({
  id,
  name,
  phoneNumber,
  email,
  onLogout,
  isBroker,
  hasSearchProfile,
  registeredWith,
}: ProfileViewProps): JSX.Element {
  const [isChangePasswordModalVisible, setChangePasswordModalVisibility] = React.useState(false);
  const [isChangeEmailModalVisible, setChangeEmailModalVisibility] = React.useState(false);
  const [isNotificationSettingsVisible, setIsNotificationSettingsVisible] = React.useState(false);
  const [isConsentSettingsVisible, setIsConsentSettingsVisible] = React.useState(false);
  const [isDeleteModalVisible, setDeleteModalVisibility] = React.useState(false);
  const [notificationState, setNotification] = React.useState<{ visibility: boolean; message: string }>({
    visibility: false,
    message: 'default-notification',
  });
  const router = useRouter();
  const ctx = useContext(GlobalCustomerContext);
  const analytics = useContext(AnalyticsContext);
  const [isTocOpen, setTocVisibility] = React.useState(false);
  const openToC = () => setTocVisibility(true);
  const closeToC = () => setTocVisibility(false);
  const [isPrivacyPolicyOpen, setPrivacyPolicyVisibility] = React.useState(false);
  const openPrivacyPolicy = () => setPrivacyPolicyVisibility(true);
  const closePrivacyPolicy = () => setPrivacyPolicyVisibility(false);
  const triggerChangePasswordModal = (shouldShow: boolean) => () => setChangePasswordModalVisibility(shouldShow);
  const triggerChangeEmailModal = (shouldShow: boolean) => () => setChangeEmailModalVisibility(shouldShow);
  const triggerDeleteModal = (shouldShow: boolean) => () => setDeleteModalVisibility(shouldShow);
  const triggerNotification = (shouldShow: boolean, message?: string) => () =>
    setNotification({
      ...notificationState,
      visibility: shouldShow,
      message,
    });
  const reopenTutorial = async (): Promise<void> => {
    const response = await CustomerRepository.factory().update({ closedTutorialAt: new Date('1970-01-01').toString() });
    if (response.isNotOk()) {
      console.error(response.getError());
    } else {
      ctx.setCustomer(response.get());
      router.push('/customer/home/<USER>');
    }
  };

  return (
    <div>
      <ToastNotification
        message={notificationState.message}
        isOpen={notificationState.visibility}
        onClose={triggerNotification(false)}
        vertical="top"
        horizontal="center"
      />
      <section className={styles.section}>
        <div className={styles.textField}>
          <TextField label={t('global.forms.fullName')} name="name" id="name" defaultValue={name} disabled />
        </div>
        <div className={styles.textField}>
          <TextField
            label={t('global.forms.phoneNumber')}
            name="phoneNumber"
            id="phoneNumber"
            defaultValue={phoneNumber}
            disabled
          />
        </div>
        <div className={styles.textField}>
          <TextField label={t('global.forms.email')} name="email" id="email" defaultValue={email} disabled />
        </div>
      </section>
      <section className={styles.section}>
        <FeatureFlag featureName={FeatureFlagName.REFERRAL}>
          {!isBroker && (
            <ProfileButton
              color="#d7b180"
              text={t('ct.profile.invite')}
              onClick={() => {
                analytics.trackEvent(
                  new ReferralJourneyAnalyticsEvent(
                    AnalyticsEventName.BUTTON_CLICK,
                    AnalyticsLocation.PROFILE_PAGE,
                    AnalyticsElement.INVITE_FRIENDS_TO_THE_NORDVIK_APP,
                  ),
                );
                router.push('/customer/invite/referrals');
              }}
            />
          )}
        </FeatureFlag>
        <div>
          <ProfileButton
            color="#454545"
            text={t('ct.profile.faq')}
            onClick={() => {
              analytics.trackEvent(
                new ReferralJourneyAnalyticsEvent(
                  AnalyticsEventName.BUTTON_CLICK,
                  AnalyticsLocation.PROFILE_PAGE,
                  AnalyticsElement.FAQ_BUTTON,
                ),
              );
              isBroker ? router.push('/broker/faq') : router.push('/customer/faq');
            }}
          />
        </div>
        <FeatureFlag featureName={FeatureFlagName.IN_APP_TUTORIAL}>
          <ProfileButton
            color="#454545"
            text={t('ct.profile.tutorial')}
            onClick={() => {
              analytics.trackEvent(
                new ReferralJourneyAnalyticsEvent(
                  AnalyticsEventName.BUTTON_CLICK,
                  AnalyticsLocation.PROFILE_PAGE,
                  AnalyticsElement.TUTORIAL_BUTTON,
                ),
              );
              reopenTutorial();
            }}
          />
        </FeatureFlag>
        <FeatureFlag
          featureName={isBroker ? FeatureFlagName.BROKER_PASSWORD_CHANGE : FeatureFlagName.CUSTOMER_PASSWORD_CHANGE}
        >
          {registeredWith !== RegisteredWith.VIPPS ? (
            <ProfileButton
              color="#454545"
              text={t('ct.profile.changePassword')}
              onClick={triggerChangePasswordModal(true)}
            />
          ) : null}
        </FeatureFlag>
        <FeatureFlag featureName={isBroker ? FeatureFlagName.EMPTY : FeatureFlagName.CUSTOMER_EMAIL_CHANGE}>
          {registeredWith !== RegisteredWith.VIPPS ? (
            <ProfileButton color="#454545" text={t('ct.profile.changeEmail')} onClick={triggerChangeEmailModal(true)} />
          ) : null}
        </FeatureFlag>
        <ProfileCookieMenu color="#454545" text={t('ct.profile.cookiePreferences')} />
        <ProfileButton
          color="#454545"
          text={t('ct.profile.notification.title')}
          onClick={() => setIsNotificationSettingsVisible(true)}
        />
        <ProfileButton
          color="#454545"
          text={t('ct.profile.consent.title')}
          onClick={() => setIsConsentSettingsVisible(true)}
        />
        {!isBroker && (
          <ProfileButton
            color="#454545"
            text={t('ct.profile.about.headline')}
            onClick={() => router.push('/customer/about')}
          />
        )}
        {!isBroker && !hasSearchProfile && (
          <div className={styles.createSearchProfile}>
            <p>{t('ct.profile.searchProfile.title')}</p>
            <p>{t('ct.profile.searchProfile.desc')}</p>
            <div className={styles.buttonContainer}>
              <GreenButton disableStopPropagation={true}>
                <a onClick={() => router.push('/customer/search-profile/housing-type')}>
                  {t('ct.profile.searchProfile.button')}
                </a>
              </GreenButton>
            </div>
          </div>
        )}
      </section>
      <section className={styles.section}>
        <div className={styles.buttonContainer}>
          <GreenBorderButton onClick={onLogout}>{t('ct.profile.logout')}</GreenBorderButton>
        </div>
        <ProfileButton color="#454545" text={t('ct.profile.privacyPolicy')} onClick={openPrivacyPolicy} />
        <ProfileButton color="#454545" text={t('ct.profile.termsAndConditions')} onClick={openToC} />
        <FeatureFlag featureName={isBroker ? FeatureFlagName.BROKER_DELETE_USER : FeatureFlagName.CUSTUMER_DELETE_USER}>
          <ProfileButton color="#454545" text={t('ct.profile.removeAccount')} onClick={triggerDeleteModal(true)} />
        </FeatureFlag>
      </section>
      <ImpersonationComponent isEmployee={ctx.customer?.isEmployee || false} />
      <div className={styles.explanation}>
        <EmailLink href={'mailto:<EMAIL>'}>{t('ct.profile.doYouHaveOtherQuestions')}</EmailLink>
        <span className={styles.inlineButton}>{t('ct.profile.nordvikEmail')}</span>
      </div>
      <FeatureFlag
        featureName={isBroker ? FeatureFlagName.BROKER_PASSWORD_CHANGE : FeatureFlagName.CUSTOMER_PASSWORD_CHANGE}
      >
        <RegularModal isOpen={isChangePasswordModalVisible} onClose={triggerChangePasswordModal(false)}>
          <h1>{t('ct.profile.changePasswordModal.title')}</h1>
          <ChangePasswordForm
            id={id}
            isBroker={isBroker}
            onClose={triggerChangePasswordModal(false)}
            onSuccess={triggerNotification(true, t('ct.profile.changePasswordModal.passwordChangeSuccess'))}
          />
        </RegularModal>
      </FeatureFlag>
      <FeatureFlag featureName={isBroker ? FeatureFlagName.EMPTY : FeatureFlagName.CUSTOMER_EMAIL_CHANGE}>
        <RegularModal isOpen={isChangeEmailModalVisible} onClose={triggerChangeEmailModal(false)}>
          <h1>{t('ct.profile.changeEmailModal.title')}</h1>
          {!isBroker && (
            <ChangeEmailAddressForm
              onClose={triggerChangeEmailModal(false)}
              onSuccess={newEmail =>
                triggerNotification(true, `${t('ct.profile.changeEmailModal.emailChangeSuccess')} ${newEmail}`)
              }
            />
          )}
        </RegularModal>
      </FeatureFlag>
      <FeatureFlag featureName={isBroker ? FeatureFlagName.EMPTY : FeatureFlagName.USER_NOTIFICATION_CENTER}>
        <RegularModal isOpen={isNotificationSettingsVisible} onClose={() => setIsNotificationSettingsVisible(false)}>
          <h1>{t('ct.profile.notification.title')}</h1>
          {!isBroker && (
            <NotificationSettingsForm onClose={() => setIsNotificationSettingsVisible(false)} onSuccess={() => {}} />
          )}
        </RegularModal>
      </FeatureFlag>
      <FeatureFlag featureName={isBroker ? FeatureFlagName.EMPTY : FeatureFlagName.USER_NOTIFICATION_CENTER}>
        <RegularModal isOpen={isConsentSettingsVisible} onClose={() => setIsConsentSettingsVisible(false)}>
          <h1>{t('ct.profile.consent.title')}</h1>
          {!isBroker && <ConsentSettingsForm onClose={() => setIsConsentSettingsVisible(false)} onSuccess={() => {}} />}
        </RegularModal>
      </FeatureFlag>
      <FeatureFlag featureName={isBroker ? FeatureFlagName.BROKER_DELETE_USER : FeatureFlagName.CUSTUMER_DELETE_USER}>
        <RegularModal isOpen={isDeleteModalVisible} onClose={triggerDeleteModal(false)}>
          <h1>{t('ct.profile.removeModal.title')}</h1>
          <DeleteUserForm id={id} onClose={triggerDeleteModal(false)} />
        </RegularModal>
      </FeatureFlag>
      <TermsAndConditionsModal isOpen={isTocOpen} onClose={closeToC} isBroker={!!isBroker} />
      <PrivacyPolicyModal isOpen={isPrivacyPolicyOpen} onClose={closePrivacyPolicy} isBroker={!!isBroker} />
    </div>
  );
}

type ProfileViewProps = {
  id: string;
  name: string;
  phoneNumber: string;
  email: string;
  onLogout: () => any;
  isBroker: boolean;
  hasSearchProfile?: CustomerProfile;
  registeredWith?: RegisteredWith;
};

export const EmailLink = styled('a')({
  textDecoration: 'none',
  color: '#174a5b !important',
});
