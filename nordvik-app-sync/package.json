{"name": "nest-typescript-starter", "private": true, "version": "1.0.0", "description": "Nest TypeScript starter repository", "license": "MIT", "scripts": {"prebuild": "rm -rf dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest --runInBand", "test:watch": "jest --verbose=false --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@hapi/joi": "^17.1.1", "@idfy/sdk": "^1.0.0-beta.6", "@nestjs/common": "^9.4.0", "@nestjs/config": "^2.3.1", "@nestjs/core": "^9.4.0", "@nestjs/mongoose": "^9.2.2", "@nestjs/passport": "^9.0.3", "@nestjs/platform-express": "^9.4.0", "@nestjs/schedule": "^2.2.1", "@nestjs/sequelize": "^9.0.2", "@sendgrid/mail": "^7.4.4", "aws-sdk": "^2.841.0", "axios": "^0.27.2", "axios-rate-limit": "^1.3.0", "axios-retry": "^3.1.9", "bluebird": "^3.7.2", "bull": "^4.10.4", "class-transformer": "^0.5.1", "class-validator": "0.14.0", "date-fns": "^2.30.0", "dd-trace": "^3.19.0", "firebase-admin": "11.7.0", "fs-extra": "^11.1.1", "jsonwebtoken": "9.0.0", "libphonenumber-js": "^1.10.12", "lodash": "^4.17.21", "moment": "2.29.4", "mongoose": "^7.1.0", "mustache": "^4.2.0", "nestjs-twilio": "^4.1.1", "os-utils": "^0.0.14", "passport": "0.6.0", "passport-headerapikey": "^1.2.2", "pg": "^8.5.1", "ramda": "^0.29.0", "recursive-readdir": "^2.2.2", "reflect-metadata": "^0.1.13", "regenerator-runtime": "^0.13.7", "rimraf": "^5.0.0", "rxjs": "^7.8.1", "sequelize": "6.31.1", "sequelize-typescript": "^2.1.5", "sharp": "^0.32.1", "sqs-consumer": "^5.5.0", "sqs-producer": "^2.1.0", "tmp-promise": "^3.0.2", "twilio": "^4.10.0", "unleash-client": "^3.4.0", "url-join": "^4.0.1", "uuid": "^9.0.0", "xml-js": "^1.6.11", "he": "1.2.0", "gql": "1.1.2"}, "devDependencies": {"@nestjs/cli": "^9.4.2", "@nestjs/schematics": "^9.1.0", "@nestjs/testing": "^9.4.0", "@types/axios": "^0.14.0", "@types/bluebird": "^3.5.33", "@types/cron": "^2.0.1", "@types/express": "^4.17.8", "@types/fs-extra": "^11.0.1", "@types/hapi__joi": "^17.1.6", "@types/jest": "^29.5.1", "@types/jsonwebtoken": "^9.0.2", "@types/moment": "^2.13.0", "@types/mustache": "^4.2.2", "@types/node": "^18.16.3", "@types/os-utils": "^0.0.1", "@types/pg": "^8.6.6", "@types/ramda": "^0.29.1", "@types/sequelize": "^4.28.15", "@types/sharp": "^0.32.0", "@types/supertest": "^2.0.10", "@types/url-join": "^4.0.0", "@types/uuid": "^9.0.1", "@types/xml-js": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "dotenv": "^16.0.3", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.1.2", "supertest": "^6.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "5.0.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}