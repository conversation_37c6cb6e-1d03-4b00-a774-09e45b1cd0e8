'use server'

import type { Session } from 'next-auth'
import { cookies } from 'next/headers'
import { cache } from 'react'

import { auth } from '@/auth'
import prisma from '@/db/prisma'
import { HOUR } from '@/db/util'
import { CACHE_KEYS } from '@/lib/cache-keys'
import nordvikApi from '@/server/nordvik-client-adaptor'
import { withCache } from '@/utils/with-cache'

import userHasAdminRole from './userHasAdminRole'

export type BrokerUser = Session['user'] & {
  department: Session['user']['department'] & { departmentId: number }
  employeeId: string
}

function isBrokerUser(user: Session['user']): user is BrokerUser {
  return !!user.employeeId && !!user.department?.departmentId
}

export const getCurrentUser = cache(async function getCurrentUser(): Promise<
  Session['user'] | undefined
> {
  const session = await auth()

  if (!session) {
    return undefined
  }

  const hasAccess = await getUserHasAccess(session.user)

  if (!hasAccess && !session.user.isImpersonated) {
    return undefined
  }

  return session.user
})

export const getCurrentUserOrThrow = cache(
  async function getCurrentUserOrThrow(): Promise<Session['user']> {
    const user = await getCurrentUser()
    if (!user) {
      throw new Error('User not found in session')
    }
    return user
  },
)

// if user is a broker, return the user with correct type
export const getCurrentUserAsBrokerOrThrow = cache(
  async function getCurrentUserAsBrokerOrThrow() {
    const user = await getCurrentUserOrThrow()
    if (!isBrokerUser(user)) {
      throw new Error('User is not a broker')
    }

    return user
  },
)

export async function isAdmin() {
  const user = await getCurrentUser()
  return userHasAdminRole(user)
}

const getUserHasAccess = async (user: Session['user']) => {
  if (user.role === 'admin') {
    return true
  }

  if (!user.employeeId) {
    return false
  }

  if (user.employeeId) {
    return isEmployeeActive(user.employeeId)
  }

  return false
}

const isEmployeeActive = cache(async (employeeId: string) =>
  withCache(
    CACHE_KEYS.USER.IS_EMPLOYEE_ACTIVE(employeeId),
    async () => {
      try {
        const activeEmployee = await nordvikApi.employeeWithRatingAndAwards({
          employeeId,
        })
        return !!activeEmployee.employee?.employeeActive
      } catch (e) {
        return false
      }
    },
    HOUR * 4,
  ),
)

export async function viewerHasAccess(estateId: string) {
  const user = await getCurrentUser()

  if (user) {
    return true
  }

  const token = (await cookies()).get('viewToken')?.value

  if (!token) {
    return false
  }

  const sellerAccessToken = await prisma.offer_access_tokens.findUnique({
    where: {
      estate_id: estateId,
      token,
      deleted_at: null,
      valid: true,
    },
  })

  return !!sellerAccessToken
}
