'use client'

import { Check, Loader2 } from 'lucide-react'
import Image from 'next/image'
import storebrandLogo from 'public/storebrand-logo.png'
import React from 'react'
import { Controller, useForm } from 'react-hook-form'

import { cn } from '@nordvik/theme/cn'
import { But<PERSON> } from '@nordvik/ui/button'
import { Label } from '@nordvik/ui/label'
import { RadioGroup, RadioGroupItem } from '@nordvik/ui/radio-group'
import { useToast } from '@nordvik/ui/toaster'

import {
  GQLInspectionLeadType,
  useStorebrandDuplicateCheckQuery,
} from '@/api/generated-client'
import { TrackPageVisit } from '@/components/track-page-visit'
import { useEstateId } from '@/hooks/use-estateid'
import { useHasSendLead } from '@/hooks/use-has-sent-lead'

import {
  useGetSignersForCurrentEstate,
  useRequestFinanceLead,
} from './financing-form-module.hooks'

export type ContactPerson = {
  contactId: string
  name: string
  asContact?: boolean
}

type FinancingFormData = {
  contactId: string
}

export function FinancingFormModule() {
  const estateId = useEstateId()

  if (!estateId) {
    throw new Error('Estate ID is missing')
  }

  const { data, isLoading: isDuplicateCheckLoading } =
    useStorebrandDuplicateCheckQuery({ input: { estateId } })

  const { data: contactPersons = [], isLoading } =
    useGetSignersForCurrentEstate() ?? []
  const {
    hasSentLead: hasRequestedFinancing,
    contactId: sentContactId,
    refetch,
  } = useHasSendLead(GQLInspectionLeadType.Financing)

  // Don't show financing form if there are existing Storebrand leads
  if (data?.storebrandDuplicateCheck.hasDuplicates) {
    return null
  }

  // Show loading state while checking for duplicates
  if (isDuplicateCheckLoading) {
    return (
      <div className="w-full max-w-[725px]">
        <div className="animate-pulse bg-gray-muted p-7 space-y-4 rounded-lg h-32" />
      </div>
    )
  }

  if (!isLoading) {
    return contactPersons.length > 0 ? (
      <FinancingForm
        key="0"
        contactPersons={contactPersons.map((person) => ({
          contactId: person.contactId,
          name: `${person.firstName} ${person.lastName}`,
          asContact: person.contactId === sentContactId,
        }))}
        hasRequestedFinancing={!!hasRequestedFinancing}
        onSubmit={refetch}
        estateId={estateId}
      />
    ) : null
  }

  return (
    <FinancingForm
      key="1"
      contactPersons={[]}
      hasRequestedFinancing={false}
      onSubmit={refetch}
      isLoading
      estateId={estateId}
    />
  )
}

function FinancingForm({
  contactPersons,
  hasRequestedFinancing,
  onSubmit,
  isLoading,
  estateId,
}: {
  contactPersons: ContactPerson[]
  hasRequestedFinancing: boolean
  onSubmit: () => void
  isLoading?: boolean
  estateId: string
}) {
  const [isSubmitting, setIsSubmitting] = React.useState(false)
  const { toast } = useToast()

  const requestFinanceLead = useRequestFinanceLead()

  const requestedContactId = contactPersons.find((p) => p.asContact)?.contactId

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FinancingFormData>({
    defaultValues: {
      contactId: requestedContactId || contactPersons[0]?.contactId,
    },
  })

  const handleFormSubmit = async (data: FinancingFormData) => {
    try {
      setIsSubmitting(true)
      await requestFinanceLead(data.contactId)
      toast({
        variant: 'success',
        title: 'Din forespørsel er sendt til Storebrand.',
      })
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Beklager, noe gikk galt. Vennligst prøv igjen senere.',
      })
    } finally {
      setIsSubmitting(false)
      onSubmit()
    }
  }

  return (
    <div className="w-full max-w-[725px]">
      <TrackPageVisit
        pageId="befaring-salesprocess/storebrand-financing-form"
        estateId={estateId}
      />
      <div className="bg-gold-muted p-7 space-y-4 rounded-lg">
        <Image src={storebrandLogo} alt="Storebrand" width={120} height={14} />

        <div className="space-y-1">
          <h4 className="typo-body-md font-medium ink-default">
            Få tilbud på finansieringsbevis eller boliglån
          </h4>
          <p className="typo-body-md ink-default">
            Hos vår samarbeidspartner Storebrand Bank får du konkurransedyktige
            betingelser og fast kontaktperson. Bli kontaktet for et
            uforpliktende tilbud.
          </p>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          <div
            className={cn('space-y-4', { hidden: contactPersons.length < 2 })}
          >
            <h3 className="typo-body-md font-medium ink-default">
              Velg kontaktperson:
            </h3>
            <Controller
              name="contactId"
              control={control}
              rules={{ required: 'Vennligst velg en kontaktperson' }}
              render={({ field }) => (
                <RadioGroup
                  disabled={isSubmitting || hasRequestedFinancing}
                  defaultValue={field.value}
                  onValueChange={field.onChange}
                  className="md:flex md:gap-8"
                >
                  {contactPersons.map((person) => (
                    <div
                      key={person.contactId}
                      className={cn('flex items-center space-x-2', {
                        'masked-placeholder-text': isLoading,
                      })}
                    >
                      <RadioGroupItem
                        disabled={isSubmitting || hasRequestedFinancing}
                        value={person.contactId}
                        id={person.contactId}
                        className="w-5 h-5 border-2"
                      />
                      <Label
                        htmlFor={person.contactId}
                        className="typo-body-md ink-default"
                      >
                        {person.name}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              )}
            />
          </div>

          {errors.contactId && (
            <p className="ink-danger text-sm mt-1">
              {errors.contactId.message}
            </p>
          )}

          {hasRequestedFinancing ? (
            <div className="flex items-center space-x-2">
              <Check size={16} className="ink-success" />
              <p className="typo-body-md ink-subtle">
                Forespørsel sendt til Storebrand.
              </p>
            </div>
          ) : (
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              loading={isLoading}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Sender forespørsel...
                </>
              ) : (
                'Send forespørsel til Storebrand'
              )}
            </Button>
          )}
        </form>
      </div>
    </div>
  )
}
