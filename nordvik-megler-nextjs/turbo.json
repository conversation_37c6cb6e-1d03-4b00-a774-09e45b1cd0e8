{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local", "package.json"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", "public/dist/**"], "env": ["AUTH_SECRET"]}, "web#build": {"dependsOn": ["^build"], "env": ["SMTP_HOST", "SMTP_USER", "SMTP_PASSWORD", "NORDVIK_API_TOKEN", "SMTP_FROM", "NO_REPLY_EMAIL", "ADPLENTY_PARTNER_API_KEY", "SIGNICAT_WEBHOOK_SECRET", "SYNC_API_URL", "SYNC_API_KEY", "NO_REPLY_EMAIL", "ADPLENTY_LOGIN_URL", "OLD_MEGLER_URL", "OLD_MEGLER_API_KEY", "PG_DATABASE_URL", "ADPLENTY_AUTH_KEY", "NEXT_PUBLIC_VERCEL_BRANCH_URL", "NEXT_PUBLIC_URL", "NEXT_PUBLIC_VERCEL_URL", "NEXT_PUBLIC_POSTHOG_KEY", "NEXT_PUBLIC_POSTHOG_HOST", "NEXT_PUBLIC_GIT_COMMIT_SHA", "NEXT_PUBLIC_VERCEL_ENV", "REDIS_REST_API_TOKEN", "REDIS_REST_API_URL", "VITEC_INSTALLATION_ID", "VITEC_URL", "VITEC_USER", "VITEC_PASSWORD", "NODE_ENV", "SENTRY_AUTH_TOKEN", "NEXT_RUNTIME", "DISABLE_CACHE", "SIGNICAT_API_URL", "NEXT_PUBLIC_FEATURE_FLAGS", "SIGNICAT_CLIENT_ID", "SIGNICAT_CLIENT_SECRET", "SIGNICAT_ACCOUNT_ID", "SLACK_FEEDBACK_WEBHOOK", "TEST_EMAIL", "TEST_PHONE", "VERCEL_ENV", "ENABLE_REACT_QUERY_DEVTOOLS", "NEXT_PUBLIC_NORDVIK_APP_URL", "NORDVIK_APP_LINK", "AWS_REGION", "AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY", "MAILCHIMP_API_KEY", "CRAFT_CMS_URL", "NORDVIK_API_URL", "SLACK_BOT_TOKEN", "SLACK_CHANNEL_ID", "GITHUB_TOKEN", "VERCEL_API_TOKEN", "NORDVIK_APP_API_URL", "NORDVIK_NO_API_URL", "NORDVIK_NO_STATS_API_URL", "NORDVIK_NO_API_KEY", "APP_JWT_SECRET", "OPENAI_API_KEY", "MONGODB_URI", "EV_CLIENT_ID", "EV_CLIENT_SECRET", "EV_API_URL", "EV_TOKEN_URL", "HANDWRITTEN_SIGNATURE", "STORYBOOK_CI"], "outputs": [".next/**", "!.next/cache/**"]}, "generate": {"cache": false, "dependsOn": ["graphql-codegen#build", "web#prisma:generate"]}, "dev": {"cache": false, "persistent": true, "dependsOn": ["web#prisma:generate"]}, "graphql-codegen#build": {}, "web#prisma:generate": {}, "web#storybook": {"persistent": true}, "@nordvik/ui#storybook": {"persistent": true}, "format": {"cache": false}, "check": {"cache": false, "dependsOn": ["lint", "check:format", "check:type"]}, "check:format": {"cache": false}, "check:type": {"cache": false}, "lint": {"cache": false}, "bundle:analyze": {"cache": false}, "bundle:size": {"cache": false}, "bundle:report": {"cache": false}, "build:analyze": {"dependsOn": ["web#prisma:generate"], "outputs": [".next/**", "!.next/cache/**"]}}, "ui": "tui"}