{"name": "nordvik-omega", "version": "1.0.0", "description": "Nordvik Omega", "node": ">=20.x.x", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build --turbopack --cache-dir=.turbo", "dev": "turbo run dev --ui=stream", "start": "turbo run start", "check": "turbo run check", "format": "turbo run format", "storybook": "turbo run storybook", "lint": "turbo run lint", "generate": "turbo run generate", "deploy-to-production": "ts-node --compiler-options '{\"module\":\"commonjs\"}' deploy-to-production.ts"}, "resolutions": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6", "eslint": "8.57.0", "graphql": "16.11.0", "next-auth": "5.0.0-beta.28", "tailwindcss": "3.4.4", "typescript": "5.8.3", "react": "19.1.0", "react-dom": "19.1.0", "react-is": "19.0.0", "eslint-config-turbo": "2.1.3"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "5.2.2", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.12", "ts-node": "10.9.2", "turbo": "2.5.5", "typescript": "5.8.3"}, "pnpm": {"overrides": {"graphql": "16.11.0", "eslint": "8.57.0", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "tailwindcss": "3.4.4", "typescript": "5.8.3", "next-auth": "5.0.0-beta.28", "react": "19.1.0", "react-dom": "19.1.0", "react-is": "19.0.0", "eslint-config-turbo": "2.1.3"}, "onlyBuiltDependencies": ["@apollo/protobufjs", "@parcel/watcher", "@prisma/client", "@prisma/engines", "@sentry/cli", "@swc/core", "@tsparticles/engine", "canvas", "core-js", "core-js-pure", "esbuild", "iltorb", "prisma", "sharp"]}, "packageManager": "pnpm@10.13.1", "dependencies": {"next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0"}}